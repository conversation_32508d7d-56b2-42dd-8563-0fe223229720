#!/usr/bin/env python3
"""
End-to-end test of the complete ASX scraper workflow
Tests: UI → scraping → PDF extraction → data output
"""

import sys
import os
import time
from datetime import datetime, timedelta

def test_streamlit_scraper_integration():
    """Test the Streamlit scraper integration"""
    print("🧪 Testing Streamlit Scraper Integration...")

    try:
        from streamlit_scraper import Streamlit<PERSON>craper, validate_scraper_config, get_asx_codes_from_input

        # Test configuration validation
        print("  Testing configuration validation...")

        # Valid config
        valid_config = {
            'selected_codes': ['14D'],  # Use a small company for testing
            'scrape_4c': True,
            'scrape_5b': True,
            'scrape_options': False,
            'date_range': 'Last 30 days',
            'headless_mode': True
        }

        errors = validate_scraper_config(valid_config)
        assert len(errors) == 0, f"Valid config should have no errors: {errors}"
        print("  ✅ Configuration validation working")

        # Test ASX code parsing
        codes_input = "14D\nCBA, BHP"
        parsed_codes = get_asx_codes_from_input(codes_input)
        expected = ['14D', 'CBA', 'BHP']
        assert parsed_codes == expected, f"Expected {expected}, got {parsed_codes}"
        print("  ✅ ASX code parsing working")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_timeframe_configuration():
    """Test timeframe configuration logic"""
    print("\n🧪 Testing Timeframe Configuration...")

    try:
        sys.path.append('sync')
        from announcement_scraper import check_date_within_timeframe

        now = datetime.now()

        # Test "All available" - should always return True
        config_all = {'type': 'All available'}
        result = check_date_within_timeframe(now, config_all)
        assert result == True, "All available should return True"
        print("  ✅ 'All available' timeframe working")

        # Test "Last 30 days"
        config_30 = {'type': 'Last 30 days'}

        # Recent date should pass
        recent_date = now - timedelta(days=15)
        result = check_date_within_timeframe(recent_date, config_30)
        assert result == True, "Recent date should pass"

        # Old date should fail
        old_date = now - timedelta(days=45)
        result = check_date_within_timeframe(old_date, config_30)
        assert result == False, "Old date should fail"
        print("  ✅ 'Last 30 days' timeframe working")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_pdf_extraction_integration():
    """Test PDF extraction with real URLs"""
    print("\n🧪 Testing PDF Extraction Integration...")

    try:
        sys.path.append('sync')
        from pdfextractor import extract_financial_data
        from pdfextractor2 import extract_unquoted_option_data

        # Test 4C extraction
        url_4c = "https://cdn-api.markitdigital.com/apiman-gateway/ASX/asx-research/1.0/file/2924-02941419-2A1593591&v=4a466cc3f899e00730cfbfcd5ab8940c41f474b6"
        result_4c = extract_financial_data(url_4c, '4C')

        assert result_4c is not None, "4C extraction should return data"
        assert 'net_cash_operating_activities' in result_4c, "Should have net_cash_operating_activities"
        assert 'cash_and_cash_equivalents' in result_4c, "Should have cash_and_cash_equivalents"
        print("  ✅ 4C PDF extraction working")

        # Test options extraction
        url_options = "https://cdn-api.markitdigital.com/apiman-gateway/ASX/asx-research/1.0/file/2924-02941419-2A1593591&v=4a466cc3f899e00730cfbfcd5ab8940c41f474b6"
        result_options = extract_unquoted_option_data(url_options)

        if result_options:  # Options might not be in every PDF
            assert 'Code' in result_options, "Should have Code field"
            print("  ✅ Options PDF extraction working")
        else:
            print("  ℹ️  No options data in test PDF (expected)")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_pagination_logic():
    """Test pagination logic without actually running browser"""
    print("\n🧪 Testing Pagination Logic...")

    try:
        # Test unlimited pagination logic
        timeframe_config_all = {'type': 'All available'}
        unlimited_pagination = (not timeframe_config_all or
                               timeframe_config_all.get('type') == 'All available' or
                               timeframe_config_all.get('type') is None)

        assert unlimited_pagination == True, "Should enable unlimited pagination for 'All available'"
        print("  ✅ Unlimited pagination logic working for 'All available'")

        # Test limited pagination logic
        timeframe_config_30 = {'type': 'Last 30 days'}
        unlimited_pagination = (not timeframe_config_30 or
                               timeframe_config_30.get('type') == 'All available' or
                               timeframe_config_30.get('type') is None)

        assert unlimited_pagination == False, "Should disable unlimited pagination for specific timeframes"
        print("  ✅ Limited pagination logic working for specific timeframes")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_data_output_format():
    """Test data output format and CSV generation"""
    print("\n🧪 Testing Data Output Format...")

    try:
        import pandas as pd

        # Test sample data structure for 4C reports
        sample_4c_data = [{
            'asx_code': '14D',
            'date': datetime.now(),
            'title': 'Appendix 4C - quarterly cash flow report',
            'url': 'https://example.com/test.pdf',
            'report_type': '4C',
            'net_cash_operating_activities': '214',
            'cash_and_cash_equivalents': '2641',
            'unused_finance_facilities': '-',
            'total_available_funding': '2641',
            'estimated_quarters_funding': 'N/A'
        }]

        df_4c = pd.DataFrame(sample_4c_data)

        # Check required columns
        required_4c_columns = [
            'asx_code', 'date', 'title', 'url', 'report_type',
            'net_cash_operating_activities', 'cash_and_cash_equivalents',
            'unused_finance_facilities', 'total_available_funding',
            'estimated_quarters_funding'
        ]

        for col in required_4c_columns:
            assert col in df_4c.columns, f"Missing required column: {col}"

        print("  ✅ 4C data structure correct")

        # Test sample data structure for options reports
        sample_options_data = [{
            'asx_code': '14D',
            'date': datetime.now(),
            'title': 'Appendix 2A - Application for quotation of securities',
            'url': 'https://example.com/test.pdf',
            'Code': '14DAH',
            'Expiry': '21 Aug 25',
            'Strike/exercise price': '$0.10',
            'Options on issue': '35,683,063'
        }]

        df_options = pd.DataFrame(sample_options_data)

        required_options_columns = [
            'asx_code', 'date', 'title', 'url',
            'Code', 'Expiry', 'Strike/exercise price', 'Options on issue'
        ]

        for col in required_options_columns:
            assert col in df_options.columns, f"Missing required column: {col}"

        print("  ✅ Options data structure correct")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_run_streamlit_launcher():
    """Test that run_streamlit.py can be imported and has required functions"""
    print("\n🧪 Testing run_streamlit.py Launcher...")

    try:
        # Check if file exists
        assert os.path.exists('run_streamlit.py'), "run_streamlit.py should exist"

        # Try to read the file and check for key functions
        with open('run_streamlit.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        required_functions = [
            'def main():',
            'def launch_streamlit(',
            'def check_venv(',
            'def create_venv('
        ]

        for func in required_functions:
            assert func in content, f"Missing function: {func}"

        print("  ✅ run_streamlit.py structure correct")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Run complete end-to-end test suite"""
    print("🚀 ASX Scraper End-to-End Test Suite")
    print("=" * 60)

    tests = [
        test_streamlit_scraper_integration,
        test_timeframe_configuration,
        test_pdf_extraction_integration,
        test_pagination_logic,
        test_data_output_format,
        test_run_streamlit_launcher
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print("\n" + "=" * 60)
    print(f"📊 End-to-End Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL END-TO-END TESTS PASSED!")
        print("✅ The complete ASX scraper workflow is working correctly")
        print("✅ Ready for client use with run_streamlit.py")
        return True
    else:
        print("⚠️  Some end-to-end tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
