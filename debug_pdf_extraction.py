#!/usr/bin/env python3
"""
Debug PDF extraction to identify field mapping issues
"""

import sys
sys.path.append('sync')
from pdfextractor import extract_financial_data, download_pdf, extract_text_from_pdf

def debug_pdf_extraction():
    """Debug a specific PDF to see what's happening"""
    
    # Use the first 4C PDF from real data
    url = "https://cdn-api.markitdigital.com/apiman-gateway/ASX/asx-research/1.0/file/2924-02941419-2A1593591&v=4a466cc3f899e00730cfbfcd5ab8940c41f474b6"
    
    print("🔍 Debugging PDF Extraction...")
    print(f"URL: {url}")
    
    # Download and extract text
    pdf_file = download_pdf(url)
    if not pdf_file:
        print("❌ Failed to download PDF")
        return
    
    text = extract_text_from_pdf(pdf_file)
    if not text:
        print("❌ Failed to extract text from PDF")
        return
    
    print("\n📄 PDF Text (Section 8):")
    lines = text.splitlines()
    section_8_lines = []
    in_section_8 = False
    
    for line in lines:
        if '8.' in line and ('Estimated cash' in line or 'Cash and cash' in line):
            in_section_8 = True
        
        if in_section_8:
            section_8_lines.append(line.strip())
            print(f"  {line.strip()}")
            
            # Stop after we've seen enough lines
            if len(section_8_lines) > 15:
                break
    
    print("\n🧪 Testing Extraction:")
    result = extract_financial_data(url, report_type='4C')
    
    if result:
        print("✅ Extraction Results:")
        for key, value in result.items():
            print(f"  {key}: {value}")
    else:
        print("❌ No extraction results")

if __name__ == "__main__":
    debug_pdf_extraction()
