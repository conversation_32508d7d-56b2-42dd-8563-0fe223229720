2025-07-22 03:03:11,004 - asx_scraper - INFO - error_logger.py:128 - Test info message | Context: test_improvements.py
2025-07-22 03:03:11,004 - asx_scraper - WARNING - error_logger.py:121 - WARNING: Test warning message | Context: test_improvements.py
2025-07-22 03:03:11,005 - asx_scraper - ERROR - error_logger.py:114 - ERROR: Test error message | Context: test_improvements.py
2025-07-22 03:03:11,005 - asx_scraper - INFO - error_logger.py:132 - ==================================================
2025-07-22 03:03:11,005 - asx_scraper - INFO - error_logger.py:133 - SCRAPING SESSION STARTED
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:134 - ASX Codes: 2
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:135 - Report Types: {'4C': True, '5B': True, 'options': False}
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:137 - Timeframe: {'type': 'Last 30 days'}
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:138 - ==================================================
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:151 - Processing page 1 for CBA - 25 rows found
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:155 - Timeframe limit reached for CBA - Found date: 2025-07-22 03:03:11.007823, Limit: Last 30 days
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:142 - ==================================================
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:143 - SCRAPING SESSION COMPLETED
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - 4c_count: 5
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - 5b_count: 3
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - options_count: 0
2025-07-22 03:03:11,008 - asx_scraper - INFO - error_logger.py:146 - total_companies: 2
2025-07-22 03:03:11,008 - asx_scraper - INFO - error_logger.py:147 - ==================================================
2025-07-22 03:05:58,116 - asx_scraper - INFO - announcement_scraper.py:456 - ============================================================
2025-07-22 03:05:58,117 - asx_scraper - INFO - announcement_scraper.py:457 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 03:05:58,117 - asx_scraper - INFO - announcement_scraper.py:458 - ASX Codes: 1
2025-07-22 03:05:58,118 - asx_scraper - INFO - announcement_scraper.py:459 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 03:05:58,118 - asx_scraper - INFO - announcement_scraper.py:462 - ============================================================
2025-07-22 03:06:00,334 - asx_scraper - INFO - announcement_scraper.py:470 - Processing ASX code: 14D
2025-07-22 03:06:24,138 - asx_scraper - INFO - announcement_scraper.py:496 - Processing page 1 for 14D
2025-07-22 03:06:24,474 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:06:29,007 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:06:33,860 - asx_scraper - DEBUG - announcement_scraper.py:286 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <li>...</li>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 03:06:33,860 - asx_scraper - INFO - announcement_scraper.py:518 - No more pages available for 14D (processed 1 pages)
2025-07-22 03:06:36,035 - asx_scraper - INFO - announcement_scraper.py:529 - ============================================================
2025-07-22 03:06:36,036 - asx_scraper - INFO - announcement_scraper.py:530 - SCRAPING SESSION COMPLETED
2025-07-22 03:06:36,037 - asx_scraper - INFO - announcement_scraper.py:531 - 4C reports found: 2
2025-07-22 03:06:36,038 - asx_scraper - INFO - announcement_scraper.py:532 - 5B reports found: 0
2025-07-22 03:06:36,039 - asx_scraper - INFO - announcement_scraper.py:533 - Options reports found: 0
2025-07-22 03:06:36,039 - asx_scraper - INFO - announcement_scraper.py:534 - ============================================================
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:456 - ============================================================
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:457 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:458 - ASX Codes: 1
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:459 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:462 - ============================================================
2025-07-22 03:08:50,268 - asx_scraper - INFO - announcement_scraper.py:470 - Processing ASX code: 14D
2025-07-22 03:08:59,958 - asx_scraper - INFO - announcement_scraper.py:496 - Processing page 1 for 14D
2025-07-22 03:09:00,370 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:09:06,113 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:09:10,371 - asx_scraper - DEBUG - announcement_scraper.py:286 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <td data-v-157f68d4="" class="hidden-xs">...</td>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 03:09:10,371 - asx_scraper - INFO - announcement_scraper.py:518 - No more pages available for 14D (processed 1 pages)
2025-07-22 03:09:12,528 - asx_scraper - INFO - announcement_scraper.py:529 - ============================================================
2025-07-22 03:09:12,530 - asx_scraper - INFO - announcement_scraper.py:530 - SCRAPING SESSION COMPLETED
2025-07-22 03:09:12,530 - asx_scraper - INFO - announcement_scraper.py:531 - 4C reports found: 2
2025-07-22 03:09:12,531 - asx_scraper - INFO - announcement_scraper.py:532 - 5B reports found: 0
2025-07-22 03:09:12,532 - asx_scraper - INFO - announcement_scraper.py:533 - Options reports found: 0
2025-07-22 03:09:12,532 - asx_scraper - INFO - announcement_scraper.py:534 - ============================================================
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 11:49:56,500 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 11:50:21,562 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 11:50:21,629 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,675 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,713 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,751 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,790 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,811 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:22,823 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,865 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,908 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,930 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 11:50:26,635 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:26,674 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:26,695 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:27,373 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:27,413 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:27,434 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 11:50:29,100 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,139 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,162 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:29,889 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,932 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,975 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,018 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,042 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:30,766 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,811 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,860 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,886 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 11:50:31,598 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,642 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,681 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,719 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,741 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 11:50:31,747 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 11:50:31,763 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 11:50:33,917 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <li class=" dropoff-menu">...</li>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 11:50:33,921 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 11:50:36,069 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 11:50:36,070 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 11:50:36,071 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 11:50:36,072 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 11:50:36,073 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 11:50:36,073 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:18:58,184 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:18:58,184 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:19:00,519 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:19:12,099 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:19:12,160 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,242 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,325 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,408 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,477 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,522 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:13,306 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,380 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,461 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,498 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:19:18,277 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:18,346 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:18,384 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:19,049 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:19,147 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:19,188 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:19:21,294 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:21,368 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:21,413 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:22,066 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,139 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,213 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,292 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,337 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:23,032 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,104 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,174 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,214 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:19:23,890 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,954 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,023 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,097 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,148 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:19:24,155 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:19:24,172 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:19:26,270 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:19:26,271 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:19:28,440 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:19:28,441 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:19:28,441 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:19:28,442 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:19:28,442 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:19:28,443 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:23:11,244 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:23:13,481 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:23:23,727 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:23:23,771 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,825 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,862 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,910 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,965 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,992 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:25,419 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,461 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,497 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,518 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:23:35,352 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:35,397 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:35,418 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:36,493 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:36,532 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:36,552 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:23:38,256 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:38,296 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:38,316 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:39,006 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,046 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,090 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,131 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,154 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:39,854 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,903 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,947 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,006 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:23:40,756 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,796 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,835 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,870 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,890 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:23:40,896 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:23:40,911 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:23:42,016 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 12:23:45,030 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 12:23:45,038 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 12:23:45,039 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:23:47,204 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:23:47,206 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:23:47,207 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:23:47,208 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:23:47,208 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:23:47,209 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:28:20,812 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:28:33,060 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:28:33,112 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,181 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,247 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,326 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,407 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,450 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:34,491 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,571 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,637 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,674 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:28:43,748 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:43,818 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:43,851 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:44,571 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:44,639 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:44,677 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:28:47,383 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:47,453 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:47,487 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:48,199 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,270 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,342 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,405 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,441 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:49,142 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,211 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,277 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,313 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:28:49,996 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,064 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,129 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,193 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,235 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:28:50,242 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:28:50,259 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:28:52,361 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:28:52,362 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:28:54,540 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:28:54,541 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:28:54,542 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:28:54,543 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:28:54,544 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:28:54,545 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:35:39,768 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:35:42,100 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:36:11,858 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:36:11,898 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:11,966 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,007 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,044 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,086 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,107 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:13,325 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,363 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,401 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,421 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:36:17,554 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:17,594 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:17,619 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:18,423 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:18,466 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:18,489 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:36:21,090 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,162 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,202 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:21,917 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,994 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,070 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,150 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,192 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:23,000 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,067 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,216 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,264 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:36:23,933 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,001 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,077 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,147 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,187 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:36:24,195 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:36:24,213 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:36:26,272 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:36:26,274 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:36:28,426 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:36:28,426 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:36:28,427 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:36:28,427 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:36:28,428 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:36:28,428 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:49:56,550 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:49:56,551 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:49:56,551 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:49:58,805 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:50:26,012 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:50:26,101 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,181 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,252 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,318 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,392 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,427 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:27,206 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,273 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,336 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,373 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:50:31,430 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:31,504 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:31,539 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:32,217 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:32,286 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:32,323 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:50:34,084 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,154 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,190 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:34,824 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,896 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,975 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,043 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,081 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:35,772 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,850 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,921 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,960 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:50:36,701 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,766 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,833 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,897 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,958 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:50:36,985 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:50:37,018 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:50:39,044 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 12:50:42,054 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 12:50:42,061 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 12:50:42,061 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:50:44,230 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:50:44,230 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 13:04:11,426 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 13:04:29,947 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 13:04:30,008 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,092 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,162 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,214 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,280 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,328 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,384 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,448 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,485 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 13:04:33,547 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,593 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,643 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,691 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,715 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 13:04:37,087 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,141 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,180 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,219 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,259 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,300 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,339 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,387 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,438 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,492 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,534 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,578 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,622 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,647 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 13:04:37,655 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 13:04:37,674 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 13:04:39,782 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1486, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 13:04:39,784 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 13:04:41,926 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 13:04:41,926 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 0
2025-07-22 13:04:41,928 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 14:03:36,179 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 14:03:38,462 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 14:03:51,337 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 14:03:51,434 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,480 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,519 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,561 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,601 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,640 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,678 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,713 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,732 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 14:03:55,869 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:55,920 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:55,971 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:56,028 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:56,053 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 14:03:57,858 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,899 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,940 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,982 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,022 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,060 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,099 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,136 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,178 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,216 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,259 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,309 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,348 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,373 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 14:03:58,381 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 14:03:58,398 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 14:03:59,482 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 14:04:02,490 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 14:04:02,494 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 14:04:02,494 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 14:04:04,600 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 14:04:04,600 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 0
2025-07-22 14:04:04,602 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:552 - ============================================================
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:553 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:554 - ASX Codes: 1
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:555 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:556 - Timeframe Config: {'type': 'All available'}
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:562 - Unlimited Pagination Mode: True
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:563 - ============================================================
2025-07-22 17:37:32,952 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 14D
2025-07-22 17:38:25,336 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.14D
2025-07-22 17:38:25,336 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 17:38:27,415 - asx_scraper - INFO - announcement_scraper.py:277 - Cookie consent popup detected - clicking 'Accept All Cookies'...
2025-07-22 17:38:28,508 - asx_scraper - INFO - announcement_scraper.py:285 - ✅ Cookie consent accepted successfully
2025-07-22 17:38:31,519 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 14D
2025-07-22 17:38:37,521 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 14D
2025-07-22 17:38:37,564 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,616 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,670 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,717 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,761 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,800 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,836 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,875 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,895 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:38:42,142 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,184 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,227 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,267 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,290 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:38:43,980 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,019 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,057 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,095 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,132 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,170 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,216 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,253 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,291 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,328 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,366 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,403 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,440 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,462 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 17:38:44,474 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 17:38:44,491 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 17:38:45,605 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 17:38:48,622 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 17:38:48,627 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 17:38:48,627 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 14D (processed 1 pages)
2025-07-22 17:38:50,751 - asx_scraper - INFO - announcement_scraper.py:633 - ============================================================
2025-07-22 17:38:50,753 - asx_scraper - INFO - announcement_scraper.py:634 - SCRAPING SESSION COMPLETED
2025-07-22 17:38:50,754 - asx_scraper - INFO - announcement_scraper.py:635 - 4C reports found: 2
2025-07-22 17:38:50,755 - asx_scraper - INFO - announcement_scraper.py:636 - 5B reports found: 0
2025-07-22 17:38:50,756 - asx_scraper - INFO - announcement_scraper.py:637 - Options reports found: 0
2025-07-22 17:38:50,757 - asx_scraper - INFO - announcement_scraper.py:638 - ============================================================
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:552 - ============================================================
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:553 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:554 - ASX Codes: 1
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:555 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:556 - Timeframe Config: {'type': 'All available'}
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:562 - Unlimited Pagination Mode: True
2025-07-22 17:55:16,093 - asx_scraper - INFO - announcement_scraper.py:563 - ============================================================
2025-07-22 17:55:18,370 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 14D
2025-07-22 17:56:18,126 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.14D
2025-07-22 17:56:18,126 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 17:56:20,152 - asx_scraper - INFO - announcement_scraper.py:277 - Cookie consent popup detected - clicking 'Accept All Cookies'...
2025-07-22 17:56:21,223 - asx_scraper - INFO - announcement_scraper.py:285 - ✅ Cookie consent accepted successfully
2025-07-22 17:56:24,240 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 14D
2025-07-22 17:56:29,241 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 14D
2025-07-22 17:56:29,293 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,341 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,395 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,436 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,477 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,518 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,557 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,595 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:29,618 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:56:34,992 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:35,031 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:35,069 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:35,105 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:35,124 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:56:36,889 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:36,930 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:36,966 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,002 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,039 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,076 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,113 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,163 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,201 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,238 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,284 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,335 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,373 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:56:37,394 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 17:56:37,401 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 17:56:37,415 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 17:56:38,513 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 17:56:41,528 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 17:56:41,533 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 17:56:41,533 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 14D (processed 1 pages)
2025-07-22 17:56:43,682 - asx_scraper - INFO - announcement_scraper.py:633 - ============================================================
2025-07-22 17:56:43,683 - asx_scraper - INFO - announcement_scraper.py:634 - SCRAPING SESSION COMPLETED
2025-07-22 17:56:43,684 - asx_scraper - INFO - announcement_scraper.py:635 - 4C reports found: 2
2025-07-22 17:56:43,685 - asx_scraper - INFO - announcement_scraper.py:636 - 5B reports found: 0
2025-07-22 17:56:43,685 - asx_scraper - INFO - announcement_scraper.py:637 - Options reports found: 0
2025-07-22 17:56:43,686 - asx_scraper - INFO - announcement_scraper.py:638 - ============================================================
2025-07-22 18:04:28,913 - asx_scraper - INFO - announcement_scraper.py:552 - ============================================================
2025-07-22 18:04:28,913 - asx_scraper - INFO - announcement_scraper.py:553 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 18:04:28,913 - asx_scraper - INFO - announcement_scraper.py:554 - ASX Codes: 18
2025-07-22 18:04:28,914 - asx_scraper - INFO - announcement_scraper.py:555 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 18:04:28,914 - asx_scraper - INFO - announcement_scraper.py:556 - Timeframe Config: {'type': 'All available'}
2025-07-22 18:04:28,914 - asx_scraper - INFO - announcement_scraper.py:562 - Unlimited Pagination Mode: True
2025-07-22 18:04:28,914 - asx_scraper - INFO - announcement_scraper.py:563 - ============================================================
2025-07-22 18:04:31,181 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 14D
2025-07-22 18:05:35,708 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.14D
2025-07-22 18:05:35,708 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:05:37,738 - asx_scraper - INFO - announcement_scraper.py:277 - Cookie consent popup detected - clicking 'Accept All Cookies'...
2025-07-22 18:05:38,814 - asx_scraper - INFO - announcement_scraper.py:285 - ✅ Cookie consent accepted successfully
2025-07-22 18:05:41,832 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 14D
2025-07-22 18:05:48,833 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 14D
2025-07-22 18:05:48,889 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:48,937 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:48,989 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:49,026 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:49,063 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:49,085 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 14D: Appendix 2A
2025-07-22 18:05:49,928 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:49,972 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:50,013 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 18:05:50,034 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:06:02,315 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:02,361 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:02,381 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 14D: Appendix 2A
2025-07-22 18:06:03,076 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:03,121 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:03,140 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:06:04,778 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:04,823 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:04,844 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 14D: Appendix 2A
2025-07-22 18:06:05,525 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:05,566 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:05,605 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:05,640 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:05,662 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 14D: Appendix 2A
2025-07-22 18:06:06,348 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:06,389 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:06,437 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:06,457 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 14D: Appendix 3G
2025-07-22 18:06:07,327 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:07,372 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:07,411 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:07,446 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 18:06:07,466 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:06:07,472 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:06:07,484 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:06:08,578 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:06:11,601 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:06:11,612 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:06:11,613 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 14D (processed 1 pages)
2025-07-22 18:06:11,613 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1AD
2025-07-22 18:06:42,681 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1AD
2025-07-22 18:06:42,682 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:06:44,718 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:06:44,727 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1AD
2025-07-22 18:06:51,729 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1AD
2025-07-22 18:06:51,773 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 00:59:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:51,813 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 00:56:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:51,838 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 2A
2025-07-22 18:06:52,579 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 07:19:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:52,620 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-30 08:50:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:52,658 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 02:27:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:52,697 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:27:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:52,718 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 2A
2025-07-22 18:06:53,892 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:27:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:53,920 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 2A
2025-07-22 18:06:54,675 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:19:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:54,717 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-11 10:44:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:54,763 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-05 07:56:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:54,816 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 03:01:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:54,866 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 00:23:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:54,900 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 2A
2025-07-22 18:06:55,573 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 00:23:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:55,596 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 2A
2025-07-22 18:06:56,247 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 01:37:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,290 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-28 02:15:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,330 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-22 01:19:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,369 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-19 00:44:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,412 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-07 01:01:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,457 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-07 00:52:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,493 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 03:11:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:56,514 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 3B (Proposed issue of securities)
2025-07-22 18:06:57,279 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:52:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:57,325 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:52:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:57,366 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:37:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:57,421 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:37:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:57,441 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AD: Appendix 3B (Proposed issue of securities)
2025-07-22 18:06:58,179 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:25:00 for 1AD (unlimited timeframe mode)
2025-07-22 18:06:58,204 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:06:58,209 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:06:58,218 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:06:59,339 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:07:02,361 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:07:02,370 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:07:02,371 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1AD (processed 1 pages)
2025-07-22 18:07:02,371 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1AE
2025-07-22 18:07:34,081 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1AE
2025-07-22 18:07:34,082 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:07:36,109 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:07:36,121 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1AE
2025-07-22 18:07:43,123 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1AE
2025-07-22 18:07:43,193 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 07:15:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:43,218 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AE: Appendix 3G
2025-07-22 18:07:43,936 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 02:51:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:43,976 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-13 06:06:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:44,011 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 01:35:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:44,033 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 1AE: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:07:45,582 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-13 09:46:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:45,632 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-02 22:19:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:45,678 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-02 22:19:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:45,714 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-31 00:21:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:45,734 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 1AE: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:07:47,768 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-16 03:46:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:47,806 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-03 05:48:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:47,843 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-19 23:09:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:47,879 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-18 23:43:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:47,914 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-17 07:47:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:47,951 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-15 22:24:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,004 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-29 04:03:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,042 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 23:41:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,081 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 23:38:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,118 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-18 23:21:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,139 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AE: Appendix 3B (Proposed issue of securities)
2025-07-22 18:07:48,827 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-18 22:33:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,867 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-18 22:31:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,905 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-31 04:21:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:48,928 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 1AE: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:07:50,124 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-28 06:03:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:50,172 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 11:26:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:50,219 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 11:12:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:50,267 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 09:20:00 for 1AE (unlimited timeframe mode)
2025-07-22 18:07:50,293 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:07:50,299 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:07:50,310 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:07:51,430 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:07:54,441 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:07:54,445 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:07:54,445 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1AE (processed 1 pages)
2025-07-22 18:07:54,446 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1AI
2025-07-22 18:08:34,351 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1AI
2025-07-22 18:08:34,351 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:08:36,367 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:08:36,373 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1AI
2025-07-22 18:08:43,375 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1AI
2025-07-22 18:08:43,417 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 00:49:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:43,457 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-08 01:01:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:43,497 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-29 00:44:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:43,517 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1AI: Quarterly Activities Report /Appendix 4C Cash Flow Statement
opens new window
2025-07-22 18:08:45,633 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-03 00:03:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:45,670 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-30 23:58:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:45,708 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-07 08:52:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:45,746 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-07 08:45:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:45,767 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AI: Appendix 2A
2025-07-22 18:08:46,385 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-26 08:39:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:46,427 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-26 08:26:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:46,470 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-28 23:05:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:46,507 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1AI: Quarterly Activities Report /Appendix 4C Cash Flow Statement
opens new window
2025-07-22 18:08:48,641 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 22:59:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:48,690 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-27 22:56:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:48,737 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-20 23:08:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:48,780 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-17 23:09:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:48,807 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AI: Appendix 3B (Proposed issue of securities)
2025-07-22 18:08:49,512 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-17 23:02:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:49,553 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-28 08:23:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:49,596 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-28 08:09:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:49,620 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AI: Appendix 3G
2025-07-22 18:08:50,287 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-25 06:48:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:50,309 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1AI: Appendix 3B (Proposed issue of securities)
2025-07-22 18:08:50,978 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-25 06:43:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:51,019 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-25 00:05:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:51,059 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-22 23:52:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:51,080 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1AI: Quarterly Activities Report /Appendix 4C Cash Flow Statement
opens new window
2025-07-22 18:08:52,292 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-24 01:01:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:52,337 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-23 09:08:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:52,381 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-06 08:27:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:52,433 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-02 10:03:00 for 1AI (unlimited timeframe mode)
2025-07-22 18:08:52,459 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:08:52,471 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:08:52,488 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:08:53,607 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:08:56,624 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:08:56,628 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:08:56,629 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1AI (processed 1 pages)
2025-07-22 18:08:56,629 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1CG
2025-07-22 18:09:36,507 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1CG
2025-07-22 18:09:36,508 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:09:38,523 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:09:38,529 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1CG
2025-07-22 18:09:43,530 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1CG
2025-07-22 18:09:43,562 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-30 00:50:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,602 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-27 08:51:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,640 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-25 08:31:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,675 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-25 08:21:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,711 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 08:50:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,748 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 08:42:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:43,769 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1CG: Appendix 2A
2025-07-22 18:09:44,482 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 08:40:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:44,521 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-11 08:16:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:44,541 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1CG: Appendix 3G
2025-07-22 18:09:45,475 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-30 07:18:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:45,518 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-27 01:00:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:45,560 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 01:10:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:45,604 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-24 00:43:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:45,625 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1CG: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:09:47,212 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:45:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:47,271 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 23:17:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:47,310 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-26 23:38:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:47,349 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-26 23:23:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:47,386 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 22:58:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:47,410 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1CG: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:09:48,847 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-20 22:21:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:48,891 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 05:37:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:48,935 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-27 07:30:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:48,957 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1CG: Appendix 3G
2025-07-22 18:09:49,722 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-26 23:35:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:49,764 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-26 23:25:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:49,806 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1CG: Appendix 2A
2025-07-22 18:09:50,607 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-17 09:00:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:50,649 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-06 09:22:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:50,704 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-17 23:40:00 for 1CG (unlimited timeframe mode)
2025-07-22 18:09:50,728 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:09:50,732 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:09:50,744 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:09:51,840 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:09:54,853 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:09:54,865 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:09:54,866 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1CG (processed 1 pages)
2025-07-22 18:09:54,867 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1MC
2025-07-22 18:10:36,644 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1MC
2025-07-22 18:10:36,644 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:10:38,674 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:10:38,686 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1MC
2025-07-22 18:10:42,687 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1MC
2025-07-22 18:10:42,720 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-15 09:43:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:42,764 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-14 03:50:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:42,804 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 04:34:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:42,825 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 2A
2025-07-22 18:10:43,545 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 11:16:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:43,584 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 01:46:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:43,620 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 04:31:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:43,666 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 11:13:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:43,688 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 2A
2025-07-22 18:10:44,699 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-02 10:09:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:44,721 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 3B (Proposed issue of securities)
2025-07-22 18:10:45,434 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-14 05:01:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:45,477 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-09 10:16:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:45,515 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-09 09:34:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:45,535 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 2A
2025-07-22 18:10:46,436 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-08 08:05:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:46,479 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-07 11:00:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:46,504 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 2A
2025-07-22 18:10:47,170 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 03:26:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:47,209 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 00:08:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:47,229 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 1MC: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:10:53,360 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-04 08:03:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:53,404 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-28 08:59:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:53,426 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 3B (Proposed issue of securities)
2025-07-22 18:10:54,257 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-27 22:22:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:54,302 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-24 08:07:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:54,341 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-10 07:33:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:54,380 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-09 22:20:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:54,418 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 22:15:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:54,440 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 1MC: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:10:56,681 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-23 09:40:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:56,723 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-23 09:38:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:56,761 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-23 06:01:00 for 1MC (unlimited timeframe mode)
2025-07-22 18:10:56,797 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1MC: Appendix 2A
2025-07-22 18:10:57,470 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:10:57,476 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:10:57,495 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:10:58,602 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:11:01,624 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:11:01,634 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:11:01,635 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1MC (processed 1 pages)
2025-07-22 18:11:01,635 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 1TTDB
2025-07-22 18:11:44,553 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.1TTDB
2025-07-22 18:11:44,553 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:11:46,561 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:11:46,565 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 1TTDB
2025-07-22 18:11:53,566 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 1TTDB
2025-07-22 18:11:53,626 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-16 04:25:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,665 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-25 08:27:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,701 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 04:39:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,737 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:31:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,775 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:25:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,812 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 01:50:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:53,832 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1TTDB: Updated Appendix 4C
opens new window
2025-07-22 18:11:55,017 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:01:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:55,042 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1TTDB: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:11:57,867 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-28 10:21:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:57,912 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-09 23:26:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:57,949 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 05:22:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:57,987 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-25 23:36:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:58,042 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-31 09:30:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:58,062 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 1TTDB: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:11:59,174 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-20 23:47:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:59,195 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1TTDB: Appendix 3B (Proposed issue of securities)
2025-07-22 18:11:59,884 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-20 23:47:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:59,929 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-16 06:23:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:11:59,967 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-16 06:23:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,004 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-07 02:10:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,046 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-07 01:27:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,084 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 00:18:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,122 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-31 06:29:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,160 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-31 00:21:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:00,180 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1TTDB: Appendix 2A
2025-07-22 18:12:01,005 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-31 00:20:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:01,059 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-18 23:31:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:01,079 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 1TTDB: Appendix 2A
2025-07-22 18:12:01,776 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-18 23:31:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:01,816 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-09 23:46:00 for 1TTDB (unlimited timeframe mode)
2025-07-22 18:12:01,837 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:12:01,841 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:12:01,850 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:12:02,970 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:12:05,993 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:12:06,002 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:12:06,003 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 1TTDB (processed 1 pages)
2025-07-22 18:12:06,003 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 29M
2025-07-22 18:12:41,817 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.29M
2025-07-22 18:12:41,817 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:12:43,836 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:12:43,843 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 29M
2025-07-22 18:12:48,843 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 29M
2025-07-22 18:12:48,878 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-17 00:24:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:48,919 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-17 00:22:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:48,957 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 02:17:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:48,995 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 02:42:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,042 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 02:39:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,080 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 07:47:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,101 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 29M: Appendix 3G
2025-07-22 18:12:49,854 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-23 04:36:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,896 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-23 00:27:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,935 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-20 00:27:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:49,973 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 02:43:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,008 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 02:41:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,047 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 02:40:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,092 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 02:36:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,133 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 02:33:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,171 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 29M: Appendix 2A
2025-07-22 18:12:50,861 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-29 00:22:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,900 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-29 00:21:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,937 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-28 00:49:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:50,974 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-23 02:38:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,011 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-23 02:34:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,051 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-23 02:31:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,087 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-07 02:34:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,124 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-03 23:16:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,144 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 29M: Appendix 3G
2025-07-22 18:12:51,887 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-03 23:11:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:51,910 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 29M: Appendix 2A
2025-07-22 18:12:52,633 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-03 23:06:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:52,673 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-28 05:44:00 for 29M (unlimited timeframe mode)
2025-07-22 18:12:52,711 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:12:52,721 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:12:52,737 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:12:53,811 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:12:56,835 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:12:56,842 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:12:56,843 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 29M (processed 1 pages)
2025-07-22 18:12:56,843 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 360
2025-07-22 18:13:39,387 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.360
2025-07-22 18:13:39,388 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:13:41,422 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:13:41,435 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 360
2025-07-22 18:13:48,436 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 360
2025-07-22 18:13:48,495 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-17 04:22:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,535 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-16 02:44:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,572 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-14 01:58:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,614 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-10 01:23:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,655 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-07 01:32:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,693 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-04 02:05:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,733 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 02:48:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,771 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 02:09:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,808 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-27 01:56:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,845 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 01:15:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,897 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 06:57:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,936 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 06:56:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:48,984 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 01:37:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,024 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 01:50:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,061 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-11 01:32:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,098 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-10 03:54:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,136 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 02:15:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,173 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 02:05:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,209 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 00:20:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,251 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-05 09:06:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,288 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 08:42:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,326 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 07:35:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,369 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 07:26:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,416 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 07:20:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,454 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 00:17:00 for 360 (unlimited timeframe mode)
2025-07-22 18:13:49,476 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:13:49,480 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:13:49,490 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:13:50,576 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:13:53,596 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:13:53,605 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:13:53,606 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 360 (processed 1 pages)
2025-07-22 18:13:53,607 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 3DA
2025-07-22 18:14:34,070 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.3DA
2025-07-22 18:14:34,071 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:14:36,083 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:14:36,087 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 3DA
2025-07-22 18:14:41,089 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 3DA
2025-07-22 18:14:41,146 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-21 18:14:41.146135 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:41,167 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 3DA: Quarterly Activities Report and Appendix 4C
opens new window
2025-07-22 18:14:42,593 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-14 07:35:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:42,635 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-14 07:29:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:42,656 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:43,309 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:59:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:43,353 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-09 07:24:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:43,375 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:44,094 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 09:00:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:44,136 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 08:58:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:44,158 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:44,929 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 01:34:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:44,974 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-07 06:52:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:45,014 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-07 06:42:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:45,051 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-04 06:37:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:45,072 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:45,916 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 01:02:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:45,959 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 10:22:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:45,981 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:46,763 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-30 01:24:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:46,813 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-26 11:07:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:46,847 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:47,601 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-26 01:54:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:47,643 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-24 01:34:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:47,688 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-24 01:25:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:47,708 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:48,669 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 01:25:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:48,709 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 10:50:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:48,760 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-17 03:05:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:48,798 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 03:39:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:48,836 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-10 10:16:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:48,859 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DA: Appendix 2A
2025-07-22 18:14:49,662 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-29 02:26:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:49,705 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-28 00:51:00 for 3DA (unlimited timeframe mode)
2025-07-22 18:14:49,726 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:14:49,731 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:14:49,740 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:14:50,824 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:14:53,846 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:14:53,856 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:14:53,856 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 3DA (processed 1 pages)
2025-07-22 18:14:53,857 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 3DP
2025-07-22 18:15:35,730 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.3DP
2025-07-22 18:15:35,731 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:15:37,743 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:15:37,747 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 3DP
2025-07-22 18:15:41,748 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 3DP
2025-07-22 18:15:41,791 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-08 00:26:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:41,832 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 01:36:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:41,854 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 3DP: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:15:47,165 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-24 22:06:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:47,208 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-19 22:19:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:47,231 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 3DP: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:15:49,197 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-12 23:25:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:49,234 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-01 22:19:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:49,271 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-26 03:23:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:49,306 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-24 22:17:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:49,347 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-30 22:11:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:49,369 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 3DP: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:15:57,251 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-29 02:09:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,324 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-29 02:06:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,384 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 01:10:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,447 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 01:03:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,507 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-09-30 01:00:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,571 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-08-30 00:05:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,632 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-08-09 00:17:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,700 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-08-08 09:10:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:57,735 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DP: Appendix 3G
2025-07-22 18:15:58,596 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-07-31 00:12:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:15:58,620 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 3DP: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 18:16:07,286 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-07-12 01:29:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:07,358 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-07-11 07:00:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:07,420 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-06-28 00:19:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:07,476 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-06-07 06:32:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:07,507 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DP: Appendix 2A
2025-07-22 18:16:08,359 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-06-05 07:41:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:08,383 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DP: Appendix 2A
2025-07-22 18:16:09,136 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-06-05 03:57:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:09,159 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3DP: Appendix 3B (Proposed issue of securities)
2025-07-22 18:16:09,894 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-06-05 03:50:00 for 3DP (unlimited timeframe mode)
2025-07-22 18:16:09,924 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:16:09,929 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:16:09,939 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:16:11,043 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:16:14,056 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:16:14,060 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:16:14,060 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 3DP (processed 1 pages)
2025-07-22 18:16:14,061 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 3PL
2025-07-22 18:16:56,028 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.3PL
2025-07-22 18:16:56,028 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:16:58,056 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:16:58,062 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 3PL
2025-07-22 18:17:02,063 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 3PL
2025-07-22 18:17:02,099 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-18 02:55:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,138 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-27 00:22:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,175 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-13 06:54:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,210 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 04:14:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,246 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-23 01:58:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,283 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-15 08:30:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,328 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-11 07:02:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,367 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-01 05:21:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,406 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-25 22:23:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,444 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-25 00:06:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,494 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-18 07:07:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,533 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-26 05:30:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,572 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-17 23:39:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,609 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-17 23:38:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,646 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-17 23:37:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,686 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-17 06:49:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,723 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-07 00:43:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,760 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-23 02:33:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,799 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-18 22:39:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,837 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 02:30:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,876 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 02:25:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,915 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-28 02:13:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:02,937 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3PL: Appendix 3G
2025-07-22 18:17:03,673 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-27 06:13:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:03,697 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 3PL: Appendix 3G
2025-07-22 18:17:04,509 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-20 03:48:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:04,555 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-19 23:35:00 for 3PL (unlimited timeframe mode)
2025-07-22 18:17:04,576 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:17:04,580 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:17:04,589 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:17:05,687 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:17:08,708 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:17:08,718 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:17:08,718 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 3PL (processed 1 pages)
2025-07-22 18:17:08,719 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 4DS
2025-07-22 18:17:50,391 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.4DS
2025-07-22 18:17:50,392 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:17:52,418 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:17:52,427 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 4DS
2025-07-22 18:17:56,429 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 4DS
2025-07-22 18:17:56,465 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 00:55:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:56,533 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 00:21:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:56,570 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-23 05:18:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:56,592 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:17:57,295 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 01:13:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:57,335 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-12 01:23:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:57,375 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-10 05:47:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:57,396 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:17:58,119 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 04:51:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:58,159 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-06 04:39:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:58,179 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:17:58,918 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 05:12:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:58,951 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:17:59,599 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-29 09:01:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:17:59,624 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:18:00,431 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-27 09:05:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:00,471 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:52:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:00,493 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 4DS: March 2025 Quarterly Activity Report and Appendix 4C
opens new window
2025-07-22 18:18:01,540 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-28 01:06:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:01,578 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-22 05:00:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:01,599 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 3G
2025-07-22 18:18:02,302 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-17 04:56:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,340 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-18 22:52:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,375 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-14 04:00:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,411 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-07 07:01:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,449 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-21 07:01:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,484 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-21 00:24:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:02,503 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 3G
2025-07-22 18:18:03,150 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-21 00:19:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:03,173 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 3G
2025-07-22 18:18:03,980 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-21 00:15:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:04,004 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:18:04,705 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-19 22:51:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:04,746 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-10 02:35:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:04,766 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DS: Appendix 2A
2025-07-22 18:18:05,466 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-31 00:06:00 for 4DS (unlimited timeframe mode)
2025-07-22 18:18:05,489 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 4DS: Quarterly Activity Report & Appendix 4C
opens new window
2025-07-22 18:18:06,572 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:18:06,582 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:18:06,598 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:18:07,736 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:18:10,758 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:18:10,768 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:18:10,769 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 4DS (processed 1 pages)
2025-07-22 18:18:10,770 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 4DX
2025-07-22 18:18:57,774 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.4DX
2025-07-22 18:18:57,774 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:18:59,791 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:18:59,798 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 4DX
2025-07-22 18:19:06,799 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 4DX
2025-07-22 18:19:06,839 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-18 01:42:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:06,881 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 07:39:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:06,922 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 05:45:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:06,959 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 05:27:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:06,996 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 05:22:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:07,017 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 2A
2025-07-22 18:19:07,727 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-17 00:53:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:07,767 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-11 00:51:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:07,805 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-10 03:40:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:07,828 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 2A
2025-07-22 18:19:08,570 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-30 06:00:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:08,613 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-29 02:30:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:08,641 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 2A
2025-07-22 18:19:09,288 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-27 02:57:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:09,328 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 01:20:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:09,367 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-05 01:50:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:09,405 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 04:00:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:09,442 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 03:49:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:09,465 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 2A
2025-07-22 18:19:10,312 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-01 03:43:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:10,352 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-29 02:31:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:10,373 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 4DX: Quarterly Activity Report and Appendix 4C for Q3 FY2025
opens new window
2025-07-22 18:19:11,653 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-24 08:02:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:11,675 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 3G
2025-07-22 18:19:12,315 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-24 07:57:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:12,339 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 4DX: Appendix 2A
2025-07-22 18:19:13,029 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:59:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,071 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:53:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,109 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-15 07:16:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,146 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-15 00:33:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,195 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-11 06:51:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,232 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-10 02:38:00 for 4DX (unlimited timeframe mode)
2025-07-22 18:19:13,254 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:19:13,259 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:19:13,271 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:19:14,339 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:19:17,359 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:19:17,369 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:19:17,369 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 4DX (processed 1 pages)
2025-07-22 18:19:17,370 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 5EA
2025-07-22 18:20:06,348 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.5EA
2025-07-22 18:20:06,348 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:20:08,377 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:20:08,390 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 5EA
2025-07-22 18:20:14,391 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 5EA
2025-07-22 18:20:14,435 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-04 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:14,474 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-04 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:14,494 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:15,224 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 00:23:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:15,262 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:15,282 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:15,900 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 00:28:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:15,924 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:16,577 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 00:28:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:16,619 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-01 00:26:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:16,655 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-26 00:21:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:16,674 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:17,319 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-04 00:22:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:17,360 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-03 00:22:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:17,413 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:17,451 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-23 01:24:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:17,472 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:18,198 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-22 00:18:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:18,239 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 00:17:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:18,260 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:18,943 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-16 00:21:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:18,982 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-14 00:22:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,019 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-14 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,041 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3B (Proposed issue of securities)
2025-07-22 18:20:19,680 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 00:27:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,721 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-28 00:09:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,759 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-24 00:22:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,801 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-09 00:20:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,841 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-02 06:10:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,880 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-01 23:25:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,923 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-31 23:55:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,974 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-31 23:52:00 for 5EA (unlimited timeframe mode)
2025-07-22 18:20:19,999 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 5EA: Appendix 3G
2025-07-22 18:20:20,666 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:20:20,673 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:20:20,683 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:20:21,801 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:20:24,823 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:20:24,829 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:20:24,829 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 5EA (processed 1 pages)
2025-07-22 18:20:24,830 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 5GG
2025-07-22 18:21:12,139 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.5GG
2025-07-22 18:21:12,140 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:21:14,168 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:21:14,181 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 5GG
2025-07-22 18:21:20,182 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 5GG
2025-07-22 18:21:20,227 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-18 05:24:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:20,270 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 09:58:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:20,308 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 09:57:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:20,346 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:18:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:20,382 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:18:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:20,403 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 5GG: Q3FY25 Appendix 4C
opens new window
2025-07-22 18:21:21,590 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:18:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,638 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-09 00:16:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,674 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-25 22:01:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,710 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-25 22:01:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,746 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-25 22:01:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,795 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-25 05:16:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,833 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-21 22:21:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,873 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-21 22:21:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:21,895 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 5GG: Q2FY25 Appendix 4C
opens new window
2025-07-22 18:21:22,960 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-21 22:21:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:23,002 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-20 06:51:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:23,021 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 5GG: Q2FY25 4C results investor briefing stream details
opens new window
2025-07-22 18:21:23,977 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-16 05:47:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,018 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-16 05:42:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,057 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-25 22:21:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,094 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-15 06:44:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,129 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 07:16:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,165 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-29 22:22:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,202 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-29 22:22:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:24,222 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 5GG: Q1FY25 Appendix 4C
opens new window
2025-07-22 18:21:25,452 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-29 22:22:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:25,505 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-27 22:24:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:25,545 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-10-08 23:22:00 for 5GG (unlimited timeframe mode)
2025-07-22 18:21:25,566 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:21:25,571 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:21:25,581 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:21:26,679 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:21:29,694 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:21:29,702 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:21:29,702 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 5GG (processed 1 pages)
2025-07-22 18:21:29,702 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 5GN
2025-07-22 18:22:00,598 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.5GN
2025-07-22 18:22:00,598 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:22:02,613 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:22:02,620 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 5GN
2025-07-22 18:22:08,622 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 5GN
2025-07-22 18:22:08,673 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-15 01:19:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,712 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-14 09:11:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,770 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-08 00:24:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,810 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 08:43:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,848 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-02 01:00:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,893 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-16 00:40:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,932 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-15 00:28:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:08,970 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-14 00:31:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,010 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-11 01:35:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,047 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-11 00:23:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,084 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-10 00:27:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,122 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-09 00:20:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,159 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-08 00:26:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,198 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-07 00:43:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,234 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-04 00:55:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,274 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-03 03:04:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,322 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-26 22:41:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,358 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-25 22:22:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,395 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-24 22:26:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,431 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-23 23:03:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,468 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-23 22:46:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,507 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-20 22:24:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,547 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-19 22:41:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,583 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-18 22:42:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,621 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-17 22:23:00 for 5GN (unlimited timeframe mode)
2025-07-22 18:22:09,641 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:22:09,646 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:22:09,657 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:22:10,766 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:22:13,784 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:22:13,793 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:22:13,793 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 5GN (processed 1 pages)
2025-07-22 18:22:13,794 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 88E
2025-07-22 18:22:54,150 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.88E
2025-07-22 18:22:54,150 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 18:22:56,177 - asx_scraper - DEBUG - announcement_scraper.py:296 - No cookie consent popup found - proceeding normally
2025-07-22 18:22:56,189 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 88E
2025-07-22 18:23:03,190 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 88E
2025-07-22 18:23:03,245 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-22 18:23:03.245801 for 88E (unlimited timeframe mode)
2025-07-22 18:23:03,268 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 88E: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:23:04,672 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-02 00:59:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,714 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-18 00:58:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,753 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-06-16 02:55:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,788 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-13 09:36:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,825 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-13 05:51:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,862 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-13 01:00:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,898 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 07:17:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,935 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 07:09:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:04,956 - asx_scraper - INFO - announcement_scraper.py:490 - Found options report for 88E: Appendix 3G
2025-07-22 18:23:06,255 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-06 06:35:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:06,306 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-17 00:30:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:06,328 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 88E: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:23:08,473 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-04 00:12:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,514 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-04 00:11:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,556 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-04 00:09:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,598 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-31 00:11:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,640 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-12 02:36:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,679 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-11 09:11:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,718 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-11 09:09:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,760 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-28 08:28:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,801 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-17 23:18:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,841 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-16 23:04:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,882 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-29 23:24:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,925 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-20 23:19:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:08,949 - asx_scraper - INFO - announcement_scraper.py:474 - Found 5B report for 88E: Quarterly Activities/Appendix 5B Cash Flow Report
opens new window
2025-07-22 18:23:13,136 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-15 07:34:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:13,181 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-31 02:14:00 for 88E (unlimited timeframe mode)
2025-07-22 18:23:13,205 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 18:23:13,210 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 18:23:13,222 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 18:23:14,286 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 18:23:17,306 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 18:23:17,312 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 18:23:17,312 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 88E (processed 1 pages)
2025-07-22 18:23:19,450 - asx_scraper - INFO - announcement_scraper.py:633 - ============================================================
2025-07-22 18:23:19,451 - asx_scraper - INFO - announcement_scraper.py:634 - SCRAPING SESSION COMPLETED
2025-07-22 18:23:19,452 - asx_scraper - INFO - announcement_scraper.py:635 - 4C reports found: 22
2025-07-22 18:23:19,453 - asx_scraper - INFO - announcement_scraper.py:636 - 5B reports found: 8
2025-07-22 18:23:19,453 - asx_scraper - INFO - announcement_scraper.py:637 - Options reports found: 44
2025-07-22 18:23:19,454 - asx_scraper - INFO - announcement_scraper.py:638 - ============================================================
