2025-07-22 03:03:11,004 - asx_scraper - INFO - error_logger.py:128 - Test info message | Context: test_improvements.py
2025-07-22 03:03:11,004 - asx_scraper - WARNING - error_logger.py:121 - WARNING: Test warning message | Context: test_improvements.py
2025-07-22 03:03:11,005 - asx_scraper - ERROR - error_logger.py:114 - ERROR: Test error message | Context: test_improvements.py
2025-07-22 03:03:11,005 - asx_scraper - INFO - error_logger.py:132 - ==================================================
2025-07-22 03:03:11,005 - asx_scraper - INFO - error_logger.py:133 - SCRAPING SESSION STARTED
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:134 - ASX Codes: 2
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:135 - Report Types: {'4C': True, '5B': True, 'options': False}
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:137 - Timeframe: {'type': 'Last 30 days'}
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:138 - ==================================================
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:151 - Processing page 1 for CBA - 25 rows found
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:155 - Timeframe limit reached for CBA - Found date: 2025-07-22 03:03:11.007823, Limit: Last 30 days
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:142 - ==================================================
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:143 - SCRAPING SESSION COMPLETED
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - 4c_count: 5
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - 5b_count: 3
2025-07-22 03:03:11,007 - asx_scraper - INFO - error_logger.py:146 - options_count: 0
2025-07-22 03:03:11,008 - asx_scraper - INFO - error_logger.py:146 - total_companies: 2
2025-07-22 03:03:11,008 - asx_scraper - INFO - error_logger.py:147 - ==================================================
2025-07-22 03:05:58,116 - asx_scraper - INFO - announcement_scraper.py:456 - ============================================================
2025-07-22 03:05:58,117 - asx_scraper - INFO - announcement_scraper.py:457 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 03:05:58,117 - asx_scraper - INFO - announcement_scraper.py:458 - ASX Codes: 1
2025-07-22 03:05:58,118 - asx_scraper - INFO - announcement_scraper.py:459 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 03:05:58,118 - asx_scraper - INFO - announcement_scraper.py:462 - ============================================================
2025-07-22 03:06:00,334 - asx_scraper - INFO - announcement_scraper.py:470 - Processing ASX code: 14D
2025-07-22 03:06:24,138 - asx_scraper - INFO - announcement_scraper.py:496 - Processing page 1 for 14D
2025-07-22 03:06:24,474 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:06:29,007 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:06:33,860 - asx_scraper - DEBUG - announcement_scraper.py:286 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <li>...</li>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 03:06:33,860 - asx_scraper - INFO - announcement_scraper.py:518 - No more pages available for 14D (processed 1 pages)
2025-07-22 03:06:36,035 - asx_scraper - INFO - announcement_scraper.py:529 - ============================================================
2025-07-22 03:06:36,036 - asx_scraper - INFO - announcement_scraper.py:530 - SCRAPING SESSION COMPLETED
2025-07-22 03:06:36,037 - asx_scraper - INFO - announcement_scraper.py:531 - 4C reports found: 2
2025-07-22 03:06:36,038 - asx_scraper - INFO - announcement_scraper.py:532 - 5B reports found: 0
2025-07-22 03:06:36,039 - asx_scraper - INFO - announcement_scraper.py:533 - Options reports found: 0
2025-07-22 03:06:36,039 - asx_scraper - INFO - announcement_scraper.py:534 - ============================================================
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:456 - ============================================================
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:457 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:458 - ASX Codes: 1
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:459 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 03:08:48,090 - asx_scraper - INFO - announcement_scraper.py:462 - ============================================================
2025-07-22 03:08:50,268 - asx_scraper - INFO - announcement_scraper.py:470 - Processing ASX code: 14D
2025-07-22 03:08:59,958 - asx_scraper - INFO - announcement_scraper.py:496 - Processing page 1 for 14D
2025-07-22 03:09:00,370 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:09:06,113 - asx_scraper - INFO - announcement_scraper.py:362 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 03:09:10,371 - asx_scraper - DEBUG - announcement_scraper.py:286 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <td data-v-157f68d4="" class="hidden-xs">...</td>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 03:09:10,371 - asx_scraper - INFO - announcement_scraper.py:518 - No more pages available for 14D (processed 1 pages)
2025-07-22 03:09:12,528 - asx_scraper - INFO - announcement_scraper.py:529 - ============================================================
2025-07-22 03:09:12,530 - asx_scraper - INFO - announcement_scraper.py:530 - SCRAPING SESSION COMPLETED
2025-07-22 03:09:12,530 - asx_scraper - INFO - announcement_scraper.py:531 - 4C reports found: 2
2025-07-22 03:09:12,531 - asx_scraper - INFO - announcement_scraper.py:532 - 5B reports found: 0
2025-07-22 03:09:12,532 - asx_scraper - INFO - announcement_scraper.py:533 - Options reports found: 0
2025-07-22 03:09:12,532 - asx_scraper - INFO - announcement_scraper.py:534 - ============================================================
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 11:49:54,238 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 11:49:54,239 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 11:49:56,500 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 11:50:21,562 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 11:50:21,629 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,675 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,713 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,751 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,790 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:21,811 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:22,823 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,865 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,908 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:22,930 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 11:50:26,635 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:26,674 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:26,695 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:27,373 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:27,413 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:27,434 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 11:50:29,100 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,139 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,162 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:29,889 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,932 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:29,975 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,018 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,042 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 11:50:30,766 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,811 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,860 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:30,886 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 11:50:31,598 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,642 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,681 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,719 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 11:50:31,741 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 11:50:31,747 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 11:50:31,763 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 11:50:33,917 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1813, 9). Other element would receive the click: <li class=" dropoff-menu">...</li>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 11:50:33,921 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 11:50:36,069 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 11:50:36,070 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 11:50:36,071 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 11:50:36,072 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 11:50:36,073 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 11:50:36,073 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:18:58,184 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:18:58,184 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:18:58,185 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:19:00,519 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:19:12,099 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:19:12,160 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,242 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,325 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,408 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,477 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:12,522 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:13,306 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,380 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,461 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:13,498 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:19:18,277 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:18,346 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:18,384 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:19,049 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:19,147 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:19,188 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:19:21,294 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:21,368 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:21,413 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:22,066 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,139 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,213 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,292 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:22,337 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:19:23,032 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,104 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,174 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,214 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:19:23,890 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:23,954 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,023 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,097 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:19:24,148 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:19:24,155 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:19:24,172 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:19:26,270 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:19:26,271 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:19:28,440 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:19:28,441 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:19:28,441 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:19:28,442 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:19:28,442 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:19:28,443 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:23:11,242 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:23:11,243 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:23:11,244 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:23:13,481 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:23:23,727 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:23:23,771 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,825 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,862 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,910 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,965 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:23,992 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:25,419 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,461 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,497 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:25,518 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:23:35,352 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:35,397 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:35,418 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:36,493 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:36,532 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:36,552 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:23:38,256 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:38,296 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:38,316 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:39,006 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,046 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,090 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,131 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,154 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:23:39,854 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,903 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:39,947 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,006 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:23:40,756 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,796 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,835 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,870 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:23:40,890 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:23:40,896 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:23:40,911 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:23:42,016 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 12:23:45,030 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 12:23:45,038 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 12:23:45,039 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:23:47,204 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:23:47,206 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:23:47,207 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:23:47,208 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:23:47,208 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:23:47,209 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:28:18,530 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:28:18,531 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:28:20,812 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:28:33,060 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:28:33,112 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,181 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,247 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,326 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,407 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:33,450 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:34,491 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,571 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,637 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:34,674 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:28:43,748 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:43,818 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:43,851 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:44,571 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:44,639 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:44,677 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:28:47,383 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:47,453 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:47,487 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:48,199 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,270 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,342 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,405 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:48,441 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:28:49,142 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,211 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,277 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:49,313 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:28:49,996 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,064 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,129 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,193 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:28:50,235 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:28:50,242 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:28:50,259 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:28:52,361 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:28:52,362 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:28:54,540 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:28:54,541 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:28:54,542 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:28:54,543 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:28:54,544 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:28:54,545 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:35:39,766 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:35:39,767 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:35:39,768 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:35:42,100 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:36:11,858 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:36:11,898 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:11,966 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,007 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,044 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,086 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:12,107 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:13,325 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,363 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,401 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:13,421 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:36:17,554 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:17,594 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:17,619 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:18,423 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:18,466 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:18,489 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:36:21,090 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,162 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,202 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:21,917 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:21,994 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,070 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,150 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:22,192 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:36:23,000 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,067 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,216 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:23,264 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:36:23,933 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,001 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,077 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,147 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:36:24,187 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:36:24,195 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:36:24,213 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:36:26,272 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1535, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 12:36:26,274 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:36:28,426 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:36:28,426 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:36:28,427 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:36:28,427 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:36:28,428 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:36:28,428 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 12:49:56,550 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 12:49:56,551 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 12:49:56,551 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: True
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 12:49:56,552 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 12:49:58,805 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 12:50:26,012 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 12:50:26,101 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,181 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,252 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,318 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,392 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:26,427 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:27,206 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,273 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,336 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:27,373 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:50:31,430 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:31,504 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:31,539 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:32,217 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:32,286 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:32,323 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 12:50:34,084 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,154 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,190 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:34,824 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,896 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:34,975 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,043 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,081 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 2A
2025-07-22 12:50:35,772 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,850 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,921 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:35,960 - asx_scraper - INFO - announcement_scraper.py:442 - Found options report for 14D: Appendix 3G
2025-07-22 12:50:36,701 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,766 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,833 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,897 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 12:50:36,958 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 12:50:36,985 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 12:50:37,018 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 12:50:39,044 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 12:50:42,054 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 12:50:42,061 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 12:50:42,061 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 12:50:44,230 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 12:50:44,230 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 5
2025-07-22 12:50:44,231 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 13:04:09,097 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 13:04:09,098 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 13:04:11,426 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 13:04:29,947 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 13:04:30,008 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,092 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,162 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,214 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,280 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,328 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,384 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,448 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:30,485 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 13:04:33,547 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,593 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,643 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,691 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:33,715 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 13:04:37,087 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,141 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,180 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,219 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,259 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,300 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,339 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,387 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,438 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,492 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,534 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,578 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,622 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 13:04:37,647 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 13:04:37,655 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 13:04:37,674 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 13:04:39,782 - asx_scraper - INFO - announcement_scraper.py:332 - No Next button found or error clicking: Message: element click intercepted: Element <a href="javascript:void(0);" class="text-upper m-l-3 p-r-2">...</a> is not clickable at point (1486, 9). Other element would receive the click: <div class="onetrust-pc-dark-filter ot-fade-in" style="z-index:2147483645"></div>
  (Session info: chrome=138.0.7204.158)
Stacktrace:
	GetHandleVerifier [0x0x331a33+62339]
	GetHandleVerifier [0x0x331a74+62404]
	(No symbol) [0x0x172123]
	(No symbol) [0x0x1c0bb0]
	(No symbol) [0x0x1bef6a]
	(No symbol) [0x0x1bcac7]
	(No symbol) [0x0x1bbd7d]
	(No symbol) [0x0x1b0515]
	(No symbol) [0x0x1df3bc]
	(No symbol) [0x0x1affa4]
	(No symbol) [0x0x1df634]
	(No symbol) [0x0x2007a3]
	(No symbol) [0x0x1df1b6]
	(No symbol) [0x0x1ae7a2]
	(No symbol) [0x0x1af644]
	GetHandleVerifier [0x0x5a65c3+2637587]
	GetHandleVerifier [0x0x5a19ca+2618138]
	GetHandleVerifier [0x0x3584aa+220666]
	GetHandleVerifier [0x0x3488d8+156200]
	GetHandleVerifier [0x0x34f06d+182717]
	GetHandleVerifier [0x0x339978+94920]
	GetHandleVerifier [0x0x339b02+95314]
	GetHandleVerifier [0x0x324c4a+9626]
	BaseThreadInitThunk [0x0x75e1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x778782ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7787827e+238]
	(No symbol) [0x0]

2025-07-22 13:04:39,784 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 13:04:41,926 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 13:04:41,926 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 13:04:41,927 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 0
2025-07-22 13:04:41,928 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 14:03:36,179 - asx_scraper - INFO - announcement_scraper.py:504 - ============================================================
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:505 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:506 - ASX Codes: 1
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:507 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:508 - Timeframe Config: {'type': 'All available'}
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:514 - Unlimited Pagination Mode: True
2025-07-22 14:03:36,180 - asx_scraper - INFO - announcement_scraper.py:515 - ============================================================
2025-07-22 14:03:38,462 - asx_scraper - INFO - announcement_scraper.py:523 - Processing ASX code: 14D
2025-07-22 14:03:51,337 - asx_scraper - INFO - announcement_scraper.py:549 - Processing page 1 for 14D
2025-07-22 14:03:51,434 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,480 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,519 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,561 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,601 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,640 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,678 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,713 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:51,732 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 14:03:55,869 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:55,920 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:55,971 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:56,028 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:56,053 - asx_scraper - INFO - announcement_scraper.py:410 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 14:03:57,858 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,899 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,940 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:57,982 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,022 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,060 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,099 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,136 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,178 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,216 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,259 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,309 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,348 - asx_scraper - DEBUG - announcement_scraper.py:381 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 14:03:58,373 - asx_scraper - INFO - announcement_scraper.py:267 - Looking for Next button...
2025-07-22 14:03:58,381 - asx_scraper - INFO - announcement_scraper.py:281 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 14:03:58,398 - asx_scraper - INFO - announcement_scraper.py:299 - Next button found and enabled - clicking...
2025-07-22 14:03:59,482 - asx_scraper - INFO - announcement_scraper.py:310 - Next button clicked successfully
2025-07-22 14:04:02,490 - asx_scraper - INFO - announcement_scraper.py:318 - New page loaded successfully
2025-07-22 14:04:02,494 - asx_scraper - WARNING - announcement_scraper.py:326 - URL didn't change after clicking Next - might be on last page
2025-07-22 14:04:02,494 - asx_scraper - INFO - announcement_scraper.py:571 - No more pages available for 14D (processed 1 pages)
2025-07-22 14:04:04,600 - asx_scraper - INFO - announcement_scraper.py:582 - ============================================================
2025-07-22 14:04:04,600 - asx_scraper - INFO - announcement_scraper.py:583 - SCRAPING SESSION COMPLETED
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:584 - 4C reports found: 2
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:585 - 5B reports found: 0
2025-07-22 14:04:04,601 - asx_scraper - INFO - announcement_scraper.py:586 - Options reports found: 0
2025-07-22 14:04:04,602 - asx_scraper - INFO - announcement_scraper.py:587 - ============================================================
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:552 - ============================================================
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:553 - STARTING ASX ANNOUNCEMENTS SCRAPING SESSION
2025-07-22 17:37:30,619 - asx_scraper - INFO - announcement_scraper.py:554 - ASX Codes: 1
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:555 - Report Types - 4C: True, 5B: True, Options: False
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:556 - Timeframe Config: {'type': 'All available'}
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:562 - Unlimited Pagination Mode: True
2025-07-22 17:37:30,620 - asx_scraper - INFO - announcement_scraper.py:563 - ============================================================
2025-07-22 17:37:32,952 - asx_scraper - INFO - announcement_scraper.py:571 - Processing ASX code: 14D
2025-07-22 17:38:25,336 - asx_scraper - INFO - announcement_scraper.py:581 - Navigated to: https://www.asx.com.au/markets/trade-our-cash-market/announcements.14D
2025-07-22 17:38:25,336 - asx_scraper - INFO - announcement_scraper.py:267 - Checking for cookie consent popup on ASX announcement page...
2025-07-22 17:38:27,415 - asx_scraper - INFO - announcement_scraper.py:277 - Cookie consent popup detected - clicking 'Accept All Cookies'...
2025-07-22 17:38:28,508 - asx_scraper - INFO - announcement_scraper.py:285 - ✅ Cookie consent accepted successfully
2025-07-22 17:38:31,519 - asx_scraper - INFO - announcement_scraper.py:589 - Announcement table loaded for 14D
2025-07-22 17:38:37,521 - asx_scraper - INFO - announcement_scraper.py:600 - Processing page 1 for 14D
2025-07-22 17:38:37,564 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:56:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,616 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-11 02:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,670 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-08 02:08:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,717 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,761 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-07-03 03:25:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,800 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-26 01:36:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,836 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-05-21 00:18:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,875 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-04-30 04:22:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:37,895 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:38:42,142 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 07:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,184 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-03-04 01:03:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,227 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-02-27 07:28:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,267 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-30 23:07:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:42,290 - asx_scraper - INFO - announcement_scraper.py:458 - Found 4C report for 14D: Quarterly Activities/Appendix 4C Cash Flow Report
opens new window
2025-07-22 17:38:43,980 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-29 04:02:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,019 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-28 23:52:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,057 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:43:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,095 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2025-01-06 23:19:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,132 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-30 06:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,170 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-29 23:20:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,216 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-12-11 23:47:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,253 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:11:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,291 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-22 03:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,328 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 05:54:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,366 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:05:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,403 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-14 01:04:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,440 - asx_scraper - DEBUG - announcement_scraper.py:429 - Processing date 2024-11-10 23:12:00 for 14D (unlimited timeframe mode)
2025-07-22 17:38:44,462 - asx_scraper - INFO - announcement_scraper.py:315 - Looking for Next button...
2025-07-22 17:38:44,474 - asx_scraper - INFO - announcement_scraper.py:329 - Found Next button with selector: //a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]
2025-07-22 17:38:44,491 - asx_scraper - INFO - announcement_scraper.py:347 - Next button found and enabled - clicking...
2025-07-22 17:38:45,605 - asx_scraper - INFO - announcement_scraper.py:358 - Next button clicked successfully
2025-07-22 17:38:48,622 - asx_scraper - INFO - announcement_scraper.py:366 - New page loaded successfully
2025-07-22 17:38:48,627 - asx_scraper - WARNING - announcement_scraper.py:374 - URL didn't change after clicking Next - might be on last page
2025-07-22 17:38:48,627 - asx_scraper - INFO - announcement_scraper.py:622 - No more pages available for 14D (processed 1 pages)
2025-07-22 17:38:50,751 - asx_scraper - INFO - announcement_scraper.py:633 - ============================================================
2025-07-22 17:38:50,753 - asx_scraper - INFO - announcement_scraper.py:634 - SCRAPING SESSION COMPLETED
2025-07-22 17:38:50,754 - asx_scraper - INFO - announcement_scraper.py:635 - 4C reports found: 2
2025-07-22 17:38:50,755 - asx_scraper - INFO - announcement_scraper.py:636 - 5B reports found: 0
2025-07-22 17:38:50,756 - asx_scraper - INFO - announcement_scraper.py:637 - Options reports found: 0
2025-07-22 17:38:50,757 - asx_scraper - INFO - announcement_scraper.py:638 - ============================================================
