# 🔧 ASX Scraper Developer Documentation

## 📋 Overview

This document provides comprehensive technical documentation for the ASX Scraper project, including architecture, data flow, debugging information, and troubleshooting guides.

## 🏗️ Architecture Overview

### **Core Components**

```
ASX Scraper Architecture
├── 🌐 Frontend (Streamlit)
│   ├── streamlit_app.py          # Main UI application
│   └── run_streamlit.py          # Application launcher
├── 🔄 Scraping Engine
│   ├── streamlit_scraper.py      # Scraping orchestrator
│   ├── scraper_functions.py     # Utility functions
│   └── sync/
│       ├── announcement_scraper.py  # Main scraper logic
│       ├── pdfextractor.py         # 4C/5B PDF processing
│       └── pdfextractor2.py        # Options PDF processing
├── 📊 Data Processing
│   ├── data_analyzer.py         # Data analysis functions
│   └── company_directory.py     # ASX company management
├── 📝 Logging & Monitoring
│   ├── error_logger.py          # Centralized logging
│   └── errors/                  # Log file storage
└── 🧪 Testing
    ├── test_improvements.py     # Feature tests
    ├── test_pagination_fix.py   # Pagination tests
    └── test_cookie_consent.py   # Cookie handling tests
```

## 🔄 Data Flow Diagram

```
User Input (Streamlit UI)
    ↓
Configuration Preparation (streamlit_scraper.py)
    ↓
ASX Announcement Pages (announcement_scraper.py)
    ↓
Cookie Consent Handling (handle_cookie_consent)
    ↓
Pagination Loop (click_next_page)
    ↓
Report Detection & Filtering (should_scrape_report_type)
    ↓
PDF Download & Processing
    ├── 4C Reports → pdfextractor.py
    ├── 5B Reports → pdfextractor.py
    └── Options → pdfextractor2.py
    ↓
Data Aggregation & Export
    ↓
Results Display (Streamlit UI)
```

## 🚀 Scraper Execution Flow

### **1. Initialization Phase**
```python
# File: streamlit_scraper.py
def run_scraper_in_thread(config, progress_queue, result_queue):
    # Setup error logging
    logger_instance = setup_error_logging()

    # Prepare timeframe configuration
    timeframe_config = {
        'type': config.get('date_range', 'All available')
    }
```

### **2. ASX Code Processing**
```python
# File: scraper_functions.py
def safe_scrape_announcements(asx_codes, headless=True, timeframe_config=None,
                             scrape_4c=True, scrape_5b=True, scrape_options=True):
    from sync.announcement_scraper import scrape_announcements
    return scrape_announcements(asx_codes, headless, timeframe_config,
                               scrape_4c, scrape_5b, scrape_options)
```

### **3. Main Scraping Loop**
```python
# File: sync/announcement_scraper.py
def scrape_announcements(asx_codes, headless=True, timeframe_config=None,
                        scrape_4c=True, scrape_5b=True, scrape_options=True):

    for asx_code in asx_codes:
        # Navigate to company announcement page
        url = f'https://www.asx.com.au/markets/trade-our-cash-market/announcements.{asx_code}'
        driver.get(url)

        # Handle cookie consent
        handle_cookie_consent(driver, wait)

        # Pagination loop
        page_num = 1
        while True:
            # Process current page
            continue_pagination = process_table_rows(...)

            # Try next page
            if click_next_page(driver, wait):
                page_num += 1
            else:
                break
```

## 🍪 Cookie Consent Handling

### **Implementation Details**
```python
# File: sync/announcement_scraper.py
def handle_cookie_consent(driver, wait):
    """Handle cookie consent popup if present"""

    # Multiple selector strategies
    cookie_selectors = [
        "button#onetrust-accept-btn-handler",
        "#onetrust-accept-btn-handler",
        "button[id='onetrust-accept-btn-handler']",
        "//button[@id='onetrust-accept-btn-handler']",
        "//button[contains(text(), 'Accept All Cookies')]"
    ]

    # Detection and clicking logic
    for selector in cookie_selectors:
        try:
            if selector.startswith("//"):
                cookie_button = driver.find_element(By.XPATH, selector)
            else:
                cookie_button = driver.find_element(By.CSS_SELECTOR, selector)

            if cookie_button.is_displayed() and cookie_button.is_enabled():
                cookie_button.click()
                return True
        except:
            continue
```

### **Integration Points**
1. **Initial Page Load**: After navigating to announcement page
2. **After Pagination**: After clicking "Next" button
3. **PDF Downloads**: When accessing individual announcement PDFs

## 📄 PDF Processing Pipeline

### **Enhanced PDF Download with Cookie Support**
```python
# File: sync/pdfextractor.py
def download_pdf(url, use_selenium=False, driver=None):
    if use_selenium and driver:
        # Navigate to PDF URL with Selenium
        driver.get(url)

        # Handle cookies
        handle_cookie_consent(driver, wait)

        # Extract cookies and use with requests
        cookies = driver.get_cookies()
        session = requests.Session()
        for cookie in cookies:
            session.cookies.set(cookie['name'], cookie['value'])

        response = session.get(current_url)
        return io.BytesIO(response.content)
```

### **Report Type Processing**
```python
# 4C Reports
financial_data = extract_financial_data(
    announcement_url, '4C', use_selenium=True, driver=driver
)

# 5B Reports
financial_data = extract_financial_data(
    announcement_url, '5B', use_selenium=True, driver=driver
)

# Options Reports
financial_data2 = extract_unquoted_option_data(
    announcement_url, code=asx_code, use_selenium=True, driver=driver
)
```

## 🔍 Debugging Guide

### **Key Files for Debugging**

#### **PDF Data Extraction Issues**
- **`sync/pdfextractor.py`** - 4C/5B financial data extraction
  - Functions: `extract_financial_data()`, `parse_financial_data()`, `clean_value()`
  - Test: `python -c "from sync.pdfextractor import test_extraction_with_sample_text; test_extraction_with_sample_text()"`

- **`sync/pdfextractor2.py`** - Options data extraction
  - Functions: `extract_unquoted_option_data()`, `parse_unquoted_option_data()`

#### **UI-Logic Integration Issues**
- **`streamlit_app.py`** - Main UI interface and user interactions
  - Functions: `show_scraper_page()`, configuration collection

- **`streamlit_scraper.py`** - Scraper orchestration and threading
  - Class: `StreamlitScraper`, Functions: `run_scraper_with_progress()`, `validate_scraper_config()`

#### **Timeframe Selection Issues**
- **`sync/announcement_scraper.py`** - Date filtering and pagination
  - Functions: `check_date_within_timeframe()`, `process_table_rows()`, `scrape_announcements()`

#### **Cookie Handling Issues**
- **`sync/announcement_scraper.py`** - Cookie consent handling
  - Functions: `handle_cookie_consent()`, `click_next_page()`

#### **End-to-End Testing**
- **`test_comprehensive_fixes.py`** - Complete test suite
- **`test_pdf_cookie_fix.py`** - PDF and cookie integration tests

### **Log File Locations**
```
errors/
├── asx_scraper_YYYYMMDD.log    # Daily log files
├── asx_scraper_YYYYMMDD.log.1  # Rotated log files
└── asx_scraper_YYYYMMDD.log.2  # Older rotated files
```

### **Key Log Messages to Look For**

#### **Successful Cookie Handling**
```
INFO - Checking for cookie consent popup...
INFO - Found cookie consent button with selector: button#onetrust-accept-btn-handler
INFO - Cookie consent popup found - clicking Accept All Cookies...
INFO - Cookie consent accepted successfully
```

#### **Pagination Progress**
```
INFO - Processing page 1 for CBA
INFO - Looking for Next button...
INFO - Found Next button with selector: //a[contains(text(), 'Next')]
INFO - Next button clicked successfully
INFO - Successfully moved to page 2 for CBA
```

#### **PDF Processing**
```
INFO - Found 4C report for CBA: Appendix 4C - Quarterly Cash Flow Report
INFO - Using Selenium to download PDF: https://...
INFO - Final PDF URL after cookie handling: https://...
```

#### **Timeframe Filtering**
```
INFO - Timeframe Config: {'type': 'All available'}
INFO - Unlimited Pagination Mode: True
INFO - Date 2024-01-15 outside timeframe Last 30 days for CBA, stopping pagination
```

### **Common Issues and Solutions**

#### **Issue: Cookie Consent Not Detected**
```
DEBUG - No cookie consent popup found or button not clickable
```
**Solution**: Check if the page has loaded completely, verify selectors are correct

#### **Issue: Pagination Fails**
```
INFO - No Next button found - likely on last page
```
**Solution**: Verify timeframe settings, check if there are actually more pages

#### **Issue: PDF Download Fails**
```
WARNING - Selenium PDF download failed, falling back to direct download
```
**Solution**: Check network connectivity, verify PDF URLs are accessible

## 🧪 Testing and Validation

### **Running Tests**
```bash
# Test all improvements
python test_improvements.py

# Test pagination specifically
python test_pagination_fix.py

# Test cookie consent handling
python test_cookie_consent.py
```

### **Manual Testing Checklist**
1. ✅ Select "All available" timeframe → Should paginate through all pages
2. ✅ Select specific timeframe → Should stop when dates exceed limit
3. ✅ Check error logs → Should show cookie consent handling
4. ✅ Verify PDF downloads → Should handle cookies for individual announcements
5. ✅ Test report filtering → Should only get selected report types

## 🔧 Configuration Options

### **Timeframe Configuration**
```python
timeframe_config = {
    'type': 'Last 30 days',        # 'All available', 'Last 30 days', etc.
    'start_date': datetime.date,    # For custom range
    'end_date': datetime.date       # For custom range
}
```

### **Scraper Configuration**
```python
config = {
    'selected_codes': ['CBA', 'BHP'],
    'scrape_4c': True,
    'scrape_5b': True,
    'scrape_options': False,
    'date_range': 'All available',
    'headless_mode': True,
    'delay_between_requests': 2
}
```

### **Logging Configuration**
```python
# File: error_logger.py
max_log_age_days = 7        # Log retention period
maxBytes = 10*1024*1024     # Max log file size (10MB)
backupCount = 5             # Number of backup files
```

## 🚨 Troubleshooting Common Issues

### **1. Scraper Stops After First Page**
**Symptoms**: Only processes page 1, doesn't continue pagination
**Causes**:
- Cookie consent blocking interaction
- Timeframe limits reached immediately
- Next button selector changed

**Debug Steps**:
1. Check logs for cookie consent messages
2. Verify timeframe configuration
3. Test with "All available" timeframe
4. Check for Next button detection logs

### **2. PDF Downloads Fail**
**Symptoms**: Reports found but no data extracted
**Causes**:
- Cookie consent required for PDF access
- Network connectivity issues
- PDF URL format changed

**Debug Steps**:
1. Check PDF download logs
2. Verify Selenium cookie handling is working
3. Test PDF URLs manually in browser
4. Check for PDF processing errors in logs

### **3. Wrong Report Types Scraped**
**Symptoms**: Getting options reports when only 4C/5B selected
**Causes**:
- Report filtering logic issues
- Document type detection problems

**Debug Steps**:
1. Check report filtering logs
2. Verify should_scrape_report_type function
3. Test with individual report types
4. Check document type extraction

## 📊 Performance Monitoring

### **Key Metrics to Monitor**
- **Pages per company**: Average pagination depth
- **Success rate**: Percentage of successful PDF downloads
- **Processing time**: Time per company/report
- **Error rate**: Frequency of errors in logs

### **Performance Optimization**
- **Delays**: Adjust delays between requests (2-4 seconds)
- **Timeframes**: Use specific timeframes to limit processing
- **Headless mode**: Use headless browser for better performance
- **Parallel processing**: Consider processing multiple companies in parallel

## 🔧 Recent Fixes Implemented

### **PDF Data Extraction Fixes** ✅
- **Fixed parentheses handling**: (424) now correctly converts to -424
- **Fixed field mapping**: 8.4 → cash_and_cash_equivalents, 8.5 → unused_finance_facilities
- **Fixed calculations**: total_available_funding and estimated_quarters_funding now calculate correctly
- **Simplified extraction logic**: Removed complex multi-line handling, improved value pattern matching

### **UI-Logic Integration** ✅
- **Threading architecture**: Proper async execution with progress tracking
- **Configuration validation**: Comprehensive validation with user-friendly error messages
- **Result display**: Real-time progress updates and detailed result summaries

### **Timeframe Selection** ✅
- **Date filtering**: All timeframe types working (All available, Last 30 days, Custom range, etc.)
- **Pagination control**: Proper stopping when timeframe limits are reached
- **Custom date ranges**: Start/end date selection working correctly

### **Testing Suite** ✅
- **Comprehensive tests**: `test_comprehensive_fixes.py` covers all major components
- **PDF extraction tests**: Validates all extraction scenarios with sample data
- **Integration tests**: Verifies UI-to-scraper communication

### **Quick Test Commands**
```bash
# Test PDF extraction
python -c "from sync.pdfextractor import test_extraction_with_sample_text; test_extraction_with_sample_text()"

# Test complete system
python test_comprehensive_fixes.py

# Test PDF and cookie integration
python test_pdf_cookie_fix.py
```

This documentation provides the foundation for understanding, debugging, and maintaining the ASX Scraper system.
