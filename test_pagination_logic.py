#!/usr/bin/env python3
"""
Test pagination logic with real ASX announcement pages
"""

import sys
sys.path.append('sync')
from announcement_scraper import setup_driver, handle_cookie_consent, click_next_page
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
import time

def test_pagination_logic():
    """Test pagination logic with a real ASX company that has multiple pages"""
    print("🧪 Testing Pagination Logic...")
    
    # Use a large company that likely has many announcements (CBA)
    asx_code = "CBA"
    url = f'https://www.asx.com.au/markets/trade-our-cash-market/announcements.{asx_code}'
    
    driver = setup_driver(headless=False)  # Use visible browser for debugging
    wait = WebDriverWait(driver, 10)
    
    try:
        print(f"Navigating to: {url}")
        driver.get(url)
        
        # Handle cookie consent
        handle_cookie_consent(driver, wait)
        
        # Wait for table to load
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "table-bordered")))
        print("✅ Table loaded successfully")
        
        # Test pagination
        page_num = 1
        max_pages = 5  # Limit for testing
        
        while page_num <= max_pages:
            print(f"\n--- Page {page_num} ---")
            
            # Check if Next button exists and is clickable
            try:
                next_selectors = [
                    "//a[contains(@class, 'text-upper') and contains(@class, 'm-l-3') and contains(@class, 'p-r-2') and contains(text(), 'Next')]",
                    "//a[contains(text(), 'Next')]",
                    "//a[@href='javascript:void(0);' and contains(text(), 'Next')]",
                    "//a[contains(@class, 'text-upper') and contains(text(), 'Next')]"
                ]
                
                next_button = None
                for selector in next_selectors:
                    try:
                        next_button = driver.find_element(By.XPATH, selector)
                        print(f"✅ Found Next button with selector: {selector}")
                        break
                    except:
                        continue
                
                if not next_button:
                    print("❌ No Next button found - likely on last page")
                    break
                
                # Check button state
                is_enabled = next_button.is_enabled()
                is_displayed = next_button.is_displayed()
                button_text = next_button.text
                
                print(f"Next button state: enabled={is_enabled}, displayed={is_displayed}, text='{button_text}'")
                
                if not is_enabled or not is_displayed:
                    print("❌ Next button found but not clickable - likely on last page")
                    break
                
                # Get current URL before clicking
                current_url = driver.current_url
                print(f"Current URL: {current_url}")
                
                # Try to click Next
                print("🔄 Attempting to click Next button...")
                success = click_next_page(driver, wait)
                
                if success:
                    new_url = driver.current_url
                    print(f"✅ Successfully moved to next page")
                    print(f"New URL: {new_url}")
                    page_num += 1
                    
                    # Wait a bit before next iteration
                    time.sleep(2)
                else:
                    print("❌ Failed to click Next button or no more pages")
                    break
                    
            except Exception as e:
                print(f"❌ Error during pagination test: {e}")
                break
        
        print(f"\n🏁 Pagination test completed. Processed {page_num} pages.")
        
    except Exception as e:
        print(f"❌ Error in pagination test: {e}")
    
    finally:
        input("Press Enter to close browser...")
        driver.quit()

def test_pagination_with_all_available():
    """Test pagination with 'All available' timeframe setting"""
    print("\n🧪 Testing Pagination with 'All available' timeframe...")
    
    # Simulate the timeframe config for "All available"
    timeframe_config = {'type': 'All available'}
    
    # This should result in unlimited pagination
    unlimited_pagination = (not timeframe_config or
                           timeframe_config.get('type') == 'All available' or
                           timeframe_config.get('type') is None)
    
    print(f"Timeframe config: {timeframe_config}")
    print(f"Unlimited pagination mode: {unlimited_pagination}")
    
    if unlimited_pagination:
        print("✅ Pagination should continue until no more Next button is available")
    else:
        print("❌ Pagination will be limited by timeframe")

if __name__ == "__main__":
    print("🚀 ASX Pagination Logic Test Suite")
    print("=" * 60)
    
    # Test the timeframe logic first
    test_pagination_with_all_available()
    
    # Test actual pagination
    test_pagination_logic()
