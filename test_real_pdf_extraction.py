#!/usr/bin/env python3
"""
Test PDF extraction with real data from results folder
"""

import pandas as pd
import sys
import os

def test_real_4c_pdf():
    """Test 4C PDF extraction with real data"""
    print("🧪 Testing 4C PDF Extraction with Real Data...")
    
    # Load real 4C data
    df = pd.read_csv('results_21_04-30-06-2025/financial_data_4C.csv')
    
    # Get first few URLs to test
    test_urls = df['url'].head(3).tolist()
    
    sys.path.append('sync')
    from pdfextractor import extract_financial_data
    
    for i, url in enumerate(test_urls):
        print(f"\n--- Testing PDF {i+1}: {url[:80]}... ---")
        
        try:
            result = extract_financial_data(url, report_type='4C')
            if result:
                print("✅ Extraction successful:")
                for key, value in result.items():
                    print(f"  {key}: {value}")
            else:
                print("❌ Extraction failed - no data returned")
                
        except Exception as e:
            print(f"❌ Extraction failed with error: {e}")

def test_real_5b_pdf():
    """Test 5B PDF extraction with real data"""
    print("\n🧪 Testing 5B PDF Extraction with Real Data...")
    
    # Load real 5B data
    df = pd.read_csv('results_21_04-30-06-2025/financial_data_5B.csv')
    
    # Get first few URLs to test
    test_urls = df['url'].head(3).tolist()
    
    sys.path.append('sync')
    from pdfextractor import extract_financial_data
    
    for i, url in enumerate(test_urls):
        print(f"\n--- Testing PDF {i+1}: {url[:80]}... ---")
        
        try:
            result = extract_financial_data(url, report_type='5B')
            if result:
                print("✅ Extraction successful:")
                for key, value in result.items():
                    print(f"  {key}: {value}")
            else:
                print("❌ Extraction failed - no data returned")
                
        except Exception as e:
            print(f"❌ Extraction failed with error: {e}")

def test_real_options_pdf():
    """Test Options PDF extraction with real data"""
    print("\n🧪 Testing Options PDF Extraction with Real Data...")
    
    # Load real options data
    df = pd.read_csv('results_21_04-30-06-2025/unquoted_option_data.csv')
    
    # Get first few URLs to test
    test_urls = df['url'].head(3).tolist()
    
    sys.path.append('sync')
    from pdfextractor2 import extract_unquoted_option_data
    
    for i, url in enumerate(test_urls):
        print(f"\n--- Testing PDF {i+1}: {url[:80]}... ---")
        
        try:
            result = extract_unquoted_option_data(url)
            if result:
                print("✅ Extraction successful:")
                for key, value in result.items():
                    print(f"  {key}: {value}")
            else:
                print("❌ Extraction failed - no data returned")
                
        except Exception as e:
            print(f"❌ Extraction failed with error: {e}")

def analyze_existing_data():
    """Analyze the existing extracted data for issues"""
    print("\n📊 Analyzing Existing Extracted Data...")
    
    # Check 4C data
    df_4c = pd.read_csv('results_21_04-30-06-2025/financial_data_4C.csv')
    print(f"\n4C Reports: {len(df_4c)} records")
    
    # Check for data quality issues
    issues = []
    
    # Check for dollar signs and commas in numeric fields
    numeric_fields = ['net_cash_operating_activities', 'cash_and_cash_equivalents', 
                     'unused_finance_facilities', 'total_available_funding']
    
    for field in numeric_fields:
        if field in df_4c.columns:
            # Count values with dollar signs
            dollar_count = df_4c[field].astype(str).str.contains(r'\$').sum()
            if dollar_count > 0:
                issues.append(f"  ❌ {field}: {dollar_count} values contain dollar signs")
            
            # Count values with commas
            comma_count = df_4c[field].astype(str).str.contains(r',').sum()
            if comma_count > 0:
                issues.append(f"  ❌ {field}: {comma_count} values contain commas")
    
    # Check 5B data
    df_5b = pd.read_csv('results_21_04-30-06-2025/financial_data_5B.csv')
    print(f"5B Reports: {len(df_5b)} records")
    
    # Check options data
    df_options = pd.read_csv('results_21_04-30-06-2025/unquoted_option_data.csv')
    print(f"Options Reports: {len(df_options)} records")
    
    if issues:
        print("\n⚠️  Data Quality Issues Found:")
        for issue in issues:
            print(issue)
    else:
        print("\n✅ No obvious data quality issues found")
    
    # Show sample data
    print(f"\nSample 4C data:")
    print(df_4c[['asx_code', 'net_cash_operating_activities', 'cash_and_cash_equivalents']].head(3))

if __name__ == "__main__":
    print("🚀 Real PDF Data Testing Suite")
    print("=" * 60)
    
    # First analyze existing data
    analyze_existing_data()
    
    # Then test with real PDFs
    test_real_4c_pdf()
    test_real_5b_pdf()
    test_real_options_pdf()
